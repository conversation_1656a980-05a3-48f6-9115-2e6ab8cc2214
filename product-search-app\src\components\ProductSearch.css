.product-search {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.search-form {
  width: 100%;
}

.search-input-container {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-input.error {
  border-color: #dc3545;
}

.search-input.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.search-buttons {
  display: flex;
  gap: 8px;
}

.search-button {
  padding: 12px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.search-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.search-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.clear-button {
  padding: 12px 16px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-button:hover {
  background-color: #545b62;
}

.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 4px;
  padding: 8px 12px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

.search-suggestions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.suggestions-label {
  font-size: 14px;
  color: #6c757d;
  margin-right: 8px;
}

.suggestion-chip {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-chip:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-input-container {
    flex-direction: column;
  }
  
  .search-buttons {
    justify-content: stretch;
  }
  
  .search-button,
  .clear-button {
    flex: 1;
  }
  
  .search-suggestions {
    justify-content: center;
  }
}
