.product-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: fit-content;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background-color: #f8f9fa;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.product-image.loading {
  opacity: 0;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  color: #6c757d;
}

.image-placeholder.error {
  background-color: #f8f9fa;
}

.image-placeholder span {
  font-size: 32px;
  margin-bottom: 8px;
}

.image-placeholder p {
  margin: 0;
  font-size: 12px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e1e5e9;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.discount-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: #dc3545;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.product-info {
  padding: 16px;
}

.product-brand {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.product-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-description {
  font-size: 14px;
  color: #6c757d;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 14px;
}

.star.full {
  color: #ffc107;
}

.star.half {
  color: #ffc107;
  position: relative;
}

.star.half::after {
  content: '☆';
  position: absolute;
  left: 50%;
  color: #dee2e6;
}

.star.empty {
  color: #dee2e6;
}

.rating-value {
  font-size: 12px;
  color: #6c757d;
}

.product-pricing {
  margin-bottom: 12px;
}

.price-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 700;
  color: #007bff;
}

.original-price {
  font-size: 14px;
  color: #6c757d;
  text-decoration: line-through;
}

.product-stock {
  margin-top: auto;
}

.stock-status {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.stock-status.in-stock {
  background-color: #d4edda;
  color: #155724;
}

.stock-status.out-of-stock {
  background-color: #f8d7da;
  color: #721c24;
}

/* Responsive design */
@media (max-width: 768px) {
  .product-card {
    border-radius: 8px;
  }
  
  .product-image-container {
    height: 180px;
  }
  
  .product-info {
    padding: 12px;
  }
  
  .product-title {
    font-size: 15px;
  }
  
  .current-price {
    font-size: 16px;
  }
}
