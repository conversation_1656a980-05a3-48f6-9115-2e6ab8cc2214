.product-list {
  width: 100%;
}

.product-list-header {
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e1e5e9;
}

.product-list-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  padding: 0;
}

.product-list-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 40px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #dee2e6;
  max-width: 400px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 20px;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
  line-height: 1.5;
}

/* Responsive design */
@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
  
  .product-list-header h2 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .empty-state {
    padding: 24px;
    margin: 16px;
  }
  
  .empty-icon {
    font-size: 36px;
  }
  
  .empty-state h3 {
    font-size: 18px;
  }
  
  .empty-state p {
    font-size: 14px;
  }
}
