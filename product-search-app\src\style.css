/* Global CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Color scheme */
  --primary-color: #007bff;
  --primary-dark: #0056b3;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;

  /* Text colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #6c757d;

  /* Background colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-light: #f8f9fa;

  /* Border colors */
  --border-color: #dee2e6;
  --border-light: #e9ecef;

  /* Shadows */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 1rem 3rem rgba(0, 0, 0, 0.175);

  /* Border radius */
  --border-radius: 0.375rem;
  --border-radius-sm: 0.25rem;
  --border-radius-lg: 0.5rem;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-primary);
}

p {
  margin: 0;
  line-height: 1.5;
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.15s ease-in-out;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Buttons */
button {
  border: none;
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  line-height: 1.5;
}

button:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Form elements */
input, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Accessibility */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-light);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}
