/* Global styles and app layout */
.app {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 24px 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 24px 0;
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-main {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px 20px;
  gap: 24px;
  align-items: flex-start;
}

.app-sidebar {
  flex: 0 0 300px;
  position: sticky;
  top: 24px;
}

.app-content {
  flex: 1;
  min-width: 0; /* Prevents flex item from overflowing */
}

/* Loading and error states */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 18px;
  color: #6c757d;
}

.loading::before {
  content: '';
  width: 24px;
  height: 24px;
  border: 3px solid #e1e5e9;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
  margin: 20px 0;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 1024px) {
  .app-main {
    padding: 16px;
    gap: 16px;
  }
  
  .app-sidebar {
    flex: 0 0 280px;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 16px;
  }
  
  .app-header h1 {
    font-size: 24px;
    margin-bottom: 16px;
  }
  
  .app-main {
    flex-direction: column;
    padding: 16px;
    gap: 16px;
  }
  
  .app-sidebar {
    flex: none;
    width: 100%;
    position: static;
    order: 1;
  }
  
  .app-content {
    order: 2;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 12px;
  }
  
  .app-header h1 {
    font-size: 20px;
  }
  
  .app-main {
    padding: 12px;
  }
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

button:focus,
input:focus,
select:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
